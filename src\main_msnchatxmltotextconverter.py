from bs4 import BeautifulSoup
from pathlib import Path


def main():
    # Read XML file
    xml_file = Path("2010.08.23-kl.23.51--sigridbuck824782058.xml")

    if not xml_file.exists():
        print(f"Error: XML file '{xml_file}' not found!")
        return

    # Read the XML content
    xml_data = xml_file.read_text(encoding="utf-8")

    # Parse XML
    soup = BeautifulSoup(xml_data, "xml")

    output_lines = []
    for msg in soup.find_all("Message"):
        date = msg.get("Date")
        time = msg.get("Time")
        frm = msg.find("From").User["FriendlyName"]
        to = msg.find("To").User["FriendlyName"]
        text = msg.Text.get_text(strip=True)

        # Format: "09.10.2008 21:32:44 | From: Jh -> To: Sigrid"
        header = f"{date} {time} | From: {frm} -> To: {to}"
        output_lines.append(header)
        output_lines.append(text)

    # Join with newlines to create the final output
    output_text = "\n".join(output_lines)

    # Print to console
    print(output_text)

    # Save to file
    output_file = Path("msn_conversation.txt")
    output_file.write_text(output_text, encoding="utf-8")
    print(f"\nOutput saved to: {output_file.absolute()}")


if __name__ == "__main__":
    main()

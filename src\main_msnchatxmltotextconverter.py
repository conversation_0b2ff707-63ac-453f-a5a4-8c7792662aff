from bs4 import BeautifulSoup
from pathlib import Path

# Full XML provided by user
xml_data = """<?xml version="1.0"?>
<?xml-stylesheet type='text/xsl' href='MessageLog.xsl'?>
<Log FirstSessionID="1" LastSessionID="12"><Message Date="09.10.2008" Time="21:32:44" DateTime="2008-10-09T19:32:44.687Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Sigrid"/>
</To><Text Style="font-family:MS Shell Dlg; color:#000000; ">du
</Text></Message><Message Date="09.10.2008" Time="21:33:06" DateTime="2008-10-09T19:33:06.796Z" SessionID="1"><From><User FriendlyName="Jh"/></From><To><User FriendlyName="Sigrid"/>
</To><Text Style="font-family:MS Shell Dlg; color:#000000; ">kan spø<PERSON> før du bøffer sprit av meg?
</Text></Message><Message Date="09.10.2008" Time="22:43:35" DateTime="2008-10-09T20:43:35.984Z" SessionID="2"><From><User FriendlyName="Sigrid"/></From><To><User FriendlyName="Jh"/>
</To><Text Style="font-family:Trebuchet MS; color:#008080; ">hææ ?
</Text></Message>..."""  # truncated here but will be the full XML

# Parse
soup = BeautifulSoup(xml_data, "xml")

output_lines = []
for msg in soup.find_all("Message"):
    date = msg.get("Date")
    time = msg.get("Time")
    frm = msg.find("From").User["FriendlyName"]
    to = msg.find("To").User["FriendlyName"]
    text = msg.Text.get_text(strip=True)
    header = f"{date} {time} | From: {frm} -> To: {to}"
    output_lines.append(header)
    output_lines.append(text)

# Save to file
output_text = "\n".join(output_lines)
print(output_text)
# outpath = Path("/mnt/data/msn_conversation.txt")
# outpath = Path("msn_conversation.txt")
# outpath.write_text(output_text, encoding="utf-8")

# outpath
